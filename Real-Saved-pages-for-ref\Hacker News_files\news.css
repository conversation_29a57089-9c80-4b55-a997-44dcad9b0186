body  { font-family:Verdana, Geneva, sans-serif; font-size:10pt; color:#828282; }
td    { font-family:Verdana, Geneva, sans-serif; font-size:10pt; color:#828282; }

.admin td   { font-family:Verdana, Geneva, sans-serif; font-size:8.5pt; color:#000000; }
.subtext td { font-family:Verdana, Geneva, sans-serif; font-size:  7pt; color:#828282; }

input    { font-family:monospace; font-size:10pt; }
input[type='submit'] { font-family:Verdana, Geneva, sans-serif; }
textarea { font-family:monospace; font-size:10pt; resize:both; }

a:link    { color:#000000; text-decoration:none; }
a:visited { color:#828282; text-decoration:none; }

.default { font-family:Verdana, Geneva, sans-serif; font-size: 10pt; color:#828282; }
.admin   { font-family:Verdana, Geneva, sans-serif; font-size:8.5pt; color:#000000; }
.title   { font-family:Verdana, Geneva, sans-serif; font-size: 10pt; color:#828282; overflow:hidden; }
.subtext { font-family:Verdana, Geneva, sans-serif; font-size:  7pt; color:#828282; }
.yclinks { font-family:Verdana, Geneva, sans-serif; font-size:  8pt; color:#828282; }
.pagetop { font-family:Verdana, Geneva, sans-serif; font-size: 10pt; color:#222222; line-height:12px; }
.comhead { font-family:Verdana, Geneva, sans-serif; font-size:  8pt; color:#828282; }
.comment { font-family:Verdana, Geneva, sans-serif; font-size:  9pt; }
.hnname  { margin-left:1px; margin-right: 5px; }

#hnmain { min-width: 796px; }

.title a { word-break: break-word; }

.comment a:link, .comment a:visited { text-decoration: underline; }
.noshow { display: none; }
.nosee { visibility: hidden; pointer-events: none; cursor: default }

.c00, .c00 a:link { color:#000000; }
.c5a, .c5a a:link, .c5a a:visited { color:#5a5a5a; }
.c73, .c73 a:link, .c73 a:visited { color:#737373; }
.c82, .c82 a:link, .c82 a:visited { color:#828282; }
.c88, .c88 a:link, .c88 a:visited { color:#888888; }
.c9c, .c9c a:link, .c9c a:visited { color:#9c9c9c; }
.cae, .cae a:link, .cae a:visited { color:#aeaeae; }
.cbe, .cbe a:link, .cbe a:visited { color:#bebebe; }
.cce, .cce a:link, .cce a:visited { color:#cecece; }
.cdd, .cdd a:link, .cdd a:visited { color:#dddddd; }
.ca2 { color:#a30000; }

.pagetop a:visited { color:#000000;}
.topsel a:link, .topsel a:visited { color:#ffffff; }

.subtext a:link, .subtext a:visited { color:#828282; }
.subtext a:hover { text-decoration:underline; }

.comhead a:link, .subtext a:visited { color:#828282; }
.comhead a:hover { text-decoration:underline; }

.hnmore a:link, a:visited { color:#828282; }
.hnmore { text-decoration:underline; }

.default p { margin-top: 8px; margin-bottom: 0px; }

.pagebreak {page-break-before:always}

pre { overflow: auto; padding: 2px; white-space: pre-wrap; overflow-wrap:anywhere; }
pre:hover { overflow:auto }

.votearrow {
  width:      10px;
  height:     10px;
  border:     0px;
  margin:     3px 2px 6px;
  background: url("triangle.svg"), linear-gradient(transparent, transparent) no-repeat;
  background-size: 10px;
}

.votelinks.nosee div.votearrow.rotate180 {
  display: none;
}

table.padtab td { padding:0px 10px }

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-device-pixel-ratio: 2) {
  .votearrow {
    background-size: 10px;
    background-image: url("triangle.svg"), linear-gradient(transparent, transparent);
  }
}

.rotate180 {
  -webkit-transform: rotate(180deg);  /* Chrome and other webkit browsers */
  -moz-transform:    rotate(180deg);  /* FF */
  -o-transform:      rotate(180deg);  /* Opera */
  -ms-transform:     rotate(180deg);  /* IE9 */
  transform:         rotate(180deg);  /* W3C complaint browsers */

  /* IE8 and below */
  -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=-1, M12=0, M21=0, M22=-1, DX=0, DY=0, SizingMethod='auto expand')";
}

/* mobile device */
@media only screen
and (min-width : 300px)
and (max-width : 750px) {
  #hnmain { width: 100%; min-width: 0; }
  body { padding: 0; margin: 0; width: 100%; }
  td { height: inherit !important; }
  .title, .comment { font-size: inherit;  }
  span.pagetop { display: block; margin: 3px 5px; font-size: 12px; line-height: normal }
  span.pagetop b { display: block; font-size: 15px; }
  table.comment-tree .comment a { display: inline-block; max-width: 200px; overflow: hidden; white-space: nowrap;
    text-overflow: ellipsis; vertical-align:top; }
  img[src='s.gif'][width='40'] { width: 12px; }
  img[src='s.gif'][width='80'] { width: 24px; }
  img[src='s.gif'][width='120'] { width: 36px; }
  img[src='s.gif'][width='160'] { width: 48px; }
  img[src='s.gif'][width='200'] { width: 60px; }
  img[src='s.gif'][width='240'] { width: 72px; }
  img[src='s.gif'][width='280'] { width: 84px; }
  img[src='s.gif'][width='320'] { width: 96px; }
  img[src='s.gif'][width='360'] { width: 108px; }
  img[src='s.gif'][width='400'] { width: 120px; }
  img[src='s.gif'][width='440'] { width: 132px; }
  img[src='s.gif'][width='480'] { width: 144px; }
  img[src='s.gif'][width='520'] { width: 156px; }
  img[src='s.gif'][width='560'] { width: 168px; }
  img[src='s.gif'][width='600'] { width: 180px; }
  img[src='s.gif'][width='640'] { width: 192px; }
  img[src='s.gif'][width='680'] { width: 204px; }
  img[src='s.gif'][width='720'] { width: 216px; }
  img[src='s.gif'][width='760'] { width: 228px; }
  img[src='s.gif'][width='800'] { width: 240px; }
  img[src='s.gif'][width='840'] { width: 252px; }
  .title { font-size: 11pt; line-height: 14pt;  }
  .subtext { font-size: 9pt; }
  .votearrow { transform: scale(1.3,1.3); margin-right: 6px; }
  .votearrow.rotate180 {
    -webkit-transform: rotate(180deg) scale(1.3,1.3);  /* Chrome and other webkit browsers */
    -moz-transform:    rotate(180deg) scale(1.3,1.3);  /* FF */
    -o-transform:      rotate(180deg) scale(1.3,1.3);  /* Opera */
    -ms-transform:     rotate(180deg) scale(1.3,1.3);  /* IE9 */
    transform:         rotate(180deg) scale(1.3,1.3);  /* W3C complaint browsers */
  }
  .votelinks { min-width: 18px; }
  .votelinks a { display: block; margin-bottom: 9px; }
  input[type='text'], input[type='number'], textarea { font-size: 16px; width: 90%; }
}

.comment { max-width: 1215px; overflow-wrap:anywhere; }



@media only screen and (min-width : 300px) and (max-width : 389px) {
  .comment { max-width: 270px; overflow: hidden }
}
@media only screen and (min-width : 390px) and (max-width : 509px) {
  .comment { max-width: 350px; overflow: hidden }
}
@media only screen and (min-width : 510px) and (max-width : 599px) {
  .comment { max-width: 460px; overflow: hidden }
}
@media only screen and (min-width : 600px) and (max-width : 689px) {
  .comment { max-width: 540px; overflow: hidden }
}
@media only screen and (min-width : 690px) and (max-width : 809px) {
  .comment { max-width: 620px; overflow: hidden }
}
@media only screen and (min-width : 810px) and (max-width : 899px) {
  .comment { max-width: 730px; overflow: hidden }
}
@media only screen and (min-width : 900px) and (max-width : 1079px) {
  .comment { max-width: 810px; overflow: hidden }
}
@media only screen and (min-width : 1080px) and (max-width : 1169px) {
  .comment { max-width: 970px; overflow: hidden }
}
@media only screen and (min-width : 1170px) and (max-width : 1259px) {
  .comment { max-width: 1050px; overflow: hidden }
}
@media only screen and (min-width : 1260px) and (max-width : 1349px) {
  .comment { max-width: 1130px; overflow: hidden }
}
