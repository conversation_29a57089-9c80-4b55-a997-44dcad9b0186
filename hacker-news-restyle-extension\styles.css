/* Hacker News Restyle – Modern, card-based layout
   Author: <PERSON> <<EMAIL>>
   Date: 2025-07-27
   URL: https://skpassegna.me
   Description: Injected CSS that modernises the look & feel of Hacker News while
   preserving its iconic orange (#ff6600). No gradients are used – only flat
   colours from YC’s palette coupled with contemporary typography, generous
   spacing and subtle shadows. Works on front page, lists, item (comment) pages,
   and auxiliary pages (FAQ, guidelines, login, security, etc.).
-------------------------------------------------------------------------- */

/* 1. Base Reset & Typography */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

html, body {
  margin: 0;
  padding: 0;
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
  font-size: 16px;
  color: #333;
  background: #f5f6f9;
  line-height: 1.6;
}

*, *::before, *::after {
  box-sizing: border-box;
}

/* Links – all states accessible */
a, a:link {
  color: #ff6600;
  text-decoration: none;
  transition: color 0.12s ease-in-out;
}

a:visited {
  color: #cc5200; /* darker orange for visited */
}

a:hover,
a:focus {
  color: #e55c00;
  text-decoration: underline;
}

a:active {
  color: #b34700;
}

/* Focus ring for keyboard nav */
a:focus-visible {
  outline: 2px dashed #ff6600;
  outline-offset: 2px;
}

A, a:link, a:visited {
  color: #ff6600;
  text-decoration: none;
}

a:hover, a:focus {
  text-decoration: underline;
}

/* 2. Layout Wrappers */
#hnmain {
  width: 100%;
  max-width: 1000px;
  margin: 24px auto;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Top orange bar (logo & nav) */
body > center > table:first-of-type {
  width: 100%;
  background: #ff6600;
  color: #fff;
  border-collapse: collapse;
  font-size: 15px;
}

body > center > table:first-of-type td {
  padding: 10px 12px;
}

body > center > table:first-of-type a {
  color: #fff;
  font-weight: 500;
}

/* Hide legacy borders */
body table {
  border: none !important;
}

/* 3. Story List (front page & lists) */
tr.athing {
  transition: background 0.15s ease-in-out;
}

tr.athing:hover {
  background: #fafafa;
}

/* Rank column */
span.rank {
  color: #999;
  font-weight: 500;
  min-width: 32px;
  display: inline-block;
}

/* Story title row */
td.title {
  padding: 14px 12px;
  font-size: 15px;
}

td.title a.storylink {
  font-weight: 600;
  color: #333;
}

td.title a.storylink:hover {
  color: #ff6600;
}

/* Subtext row (score, by, time, comments) */
td.subtext {
  padding: 0 12px 16px 54px;
  font-size: 13px;
  color: #777;
}

/* Metadata spacing */
td.subtext span {
  margin-right: 12px;
}

/* 4. Comment Threads */
/* Indent guides */

/* Each indent level uses an .ind img[width="0"] holder. We add a thin guide */
.ind {
  position: relative;
  width: 40px !important;
}

.ind:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background: #e8e8e8;
}

/* Comment container */
.comment {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 8px 0 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* Comment header (author & time) */
.comment span.comhead {
  display: block;
  margin-bottom: 8px;
  font-size: 13px;
  color: #555;
}

.comment span.comhead a {
  color: #ff6600;
  font-weight: 500;
}

/* Comment text */
.comment span.commtext {
  font-size: 15px;
  color: #333;
  line-height: 1.55;
}

/* Reply / hide links */
.comment span.comhead + a, /* ‘parent’ link */
.comment a[href^="reply"],
.comment a[href^="item?id="] {
  margin-left: 16px;
  font-size: 13px;
  color: #999;
}

.comment a[href^="reply"]:hover {
  color: #ff6600;
}

/* Collapsed comment hint */

font[color="grey"] {
  color: #777 !important;
}

/* 5. Pagination */
.title + tr a.morelink {
  display: inline-block;
  margin: 24px 0;
  padding: 10px 20px;
  border: 1px solid #ff6600;
  border-radius: 6px;
  color: #ff6600;
  font-weight: 500;
  transition: background 0.15s ease-in-out, color 0.15s ease-in-out;
}

a.morelink:hover {
  background: #ff6600;
  color: #fff;
}

/* 6. Misc pages (FAQ, guidelines, etc.) */
body center > table:nth-of-type(2) {
  max-width: 800px;
  margin: 24px auto;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.05);
  padding: 24px 32px;
}

body center > table:nth-of-type(2) td {
  font-size: 15px;
  color: #333;
  line-height: 1.65;
}

/* 7. Login page */
input[type="text"],
input[type="password"] {
  appearance: none;

  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 15px;
}

input[type="submit"],
input[type="button"] {
  background: #ff6600;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 12px 20px;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.15s ease-in-out;
}

input[type="submit"]:hover,
input[type="submit"]:focus,
input[type="button"]:focus {
  background: #e55c00;
  outline: 2px dashed #b34700;
  outline-offset: 2px;
}

input[type="submit"]:hover,
input[type="button"]:hover {
  background: #e55c00;
}

/* 8. Voting arrows & micro elements */
/* Align vote arrow container */
.votelinks {
  width: 24px;
}

.votearrow {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 10px solid #ccc; /* neutral grey */
  margin: 0 auto;
  transition: border-bottom-color 0.15s ease-in-out;
}

/* Highlight on hover */
.votelinks a:hover .votearrow {
  border-bottom-color: #ff6600;
}

/* Score, age & metadata */
.score { color: #ff6600; font-weight: 600; }
.age, .subtext a.hnpast { color: #777; }

.hnuser { color: #333; font-weight: 500; }
.hnuser:hover { color: #ff6600; }

/* Site domain next to title */
.sitebit .sitestr { color: #777; }

/* Toggle & nav links inside comments */
.togg, .navs a { color: #999; font-size: 12px; }
.togg:hover, .navs a:hover { color: #ff6600; }

/* Textarea (add comment) full width */
textarea[name="text"] {
  width: 100% !important;
  resize: vertical;
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 10px 12px;
  font-family: inherit;
  font-size: 15px;
}

/* 9. Utilities */
.hide-legacy-separators br + br {
  display: none;
}

/* Responsive tweaks */
@media (max-width: 600px) {
  #hnmain {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  td.title {
    padding: 12px 8px;
    font-size: 14px;
  }

  td.subtext {
    padding-left: 44px;
    font-size: 12px;
  }

  .comment {
    padding: 10px 12px;
  }

  body > center > table:first-of-type {
    font-size: 14px;
  }
}
